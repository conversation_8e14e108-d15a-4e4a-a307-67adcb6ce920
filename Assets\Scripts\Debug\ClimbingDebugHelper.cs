using UnityEngine;
using Fusion;

/// <summary>
/// Вспомогательный скрипт для отладки системы скалолазания
/// Помогает выявить проблемы с синхронизацией позиции
/// </summary>
public class ClimbingDebugHelper : NetworkBehaviour {
    [Header("Debug Settings")]
    public bool enableDebugLogs = true;
    public bool showDebugGUI = true;
    public KeyCode debugToggleKey = KeyCode.F6;
    
    private PlayerController playerController;
    private Vector3 lastPosition;
    private float positionChangeThreshold = 0.01f;
    
    public override void Spawned() {
        playerController = GetComponent<PlayerController>();
        if (playerController != null) {
            lastPosition = transform.position;
        }
    }
    
    private void Update() {
        if (Input.GetKeyDown(debugToggleKey)) {
            showDebugGUI = !showDebugGUI;
        }
    }
    
    public override void FixedUpdateNetwork() {
        if (!enableDebugLogs || playerController == null) return;
        
        // Отслеживаем изменения позиции во время скалолазания
        if (playerController.IsClimbing) {
            Vector3 currentPosition = transform.position;
            float positionDelta = Vector3.Distance(lastPosition, currentPosition);
            
            if (positionDelta > positionChangeThreshold) {
                string authorityInfo = Object.HasStateAuthority ? "STATE_AUTHORITY" : "CLIENT";
                string inputInfo = Object.HasInputAuthority ? "INPUT_AUTHORITY" : "NO_INPUT";
                
                Debug.Log($"[ClimbingDebug] {authorityInfo} | {inputInfo} | Position changed by {positionDelta:F4} | " +
                         $"From: {lastPosition} To: {currentPosition}");
            }
            
            lastPosition = currentPosition;
        }
    }
    
    private void OnGUI() {
        if (!showDebugGUI || playerController == null) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 200));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("=== CLIMBING DEBUG INFO ===");
        GUILayout.Label($"Is Climbing: {playerController.IsClimbing}");
        GUILayout.Label($"Current Hold: {(playerController.CurrentHold != null ? playerController.CurrentHold.name : "None")}");
        GUILayout.Label($"Has State Authority: {Object.HasStateAuthority}");
        GUILayout.Label($"Has Input Authority: {Object.HasInputAuthority}");
        GUILayout.Label($"Position: {transform.position}");
        
        if (playerController.IsClimbing && playerController.CurrentHold != null) {
            ClimbingHold hold = playerController.CurrentHold.GetComponent<ClimbingHold>();
            if (hold != null) {
                GUILayout.Label($"Hold Position: {hold.GetHoldPosition()}");
                float distance = Vector3.Distance(transform.position, hold.GetHoldPosition());
                GUILayout.Label($"Distance to Hold: {distance:F2}");
            }
        }
        
        GUILayout.Label($"Press {debugToggleKey} to toggle debug");
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
